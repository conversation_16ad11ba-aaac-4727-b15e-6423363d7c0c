<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Fast gradient Perlin noise</Name>
  <Location>/Engine/Shaders/Private/Random.usf</Location>
  <Date>2016-06-09T17:10:42.1914383-04:00</Date>
  <Function>Function that computes coherent noise from a given position in n dimensions</Function>
  <Justification>Faster version of the existing perlin noise, similar quality</Justification>
  <Eula>None available, see http://prettyprocs.wordpress.com/2012/10/20/fast-perlin-noise</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>