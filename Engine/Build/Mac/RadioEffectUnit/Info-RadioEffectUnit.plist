<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AudioComponents</key>
	<array>
		<dict>
			<key>sandboxSafe</key>
			<true/>
			<key>description</key>
			<string>Radio effect for Unreal Engine</string>
			<key>factoryFunction</key>
			<string>RadioEffectUnitFactory</string>
			<key>manufacturer</key>
			<string>Epic</string>
			<key>name</key>
			<string>Epic Games: RadioEffectUnit</string>
			<key>subtype</key>
			<string>Rdio</string>
			<key>type</key>
			<string>aufx</string>
			<key>version</key>
			<integer>65536</integer>
		</dict>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleExecutable</key>
	<string>RadioEffectUnit</string>
	<key>CFBundleIconFile</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>com.epicgames.audiounit.radio</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundlePackageType</key>
	<string>BNDL</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1.0.0</string>
	<key>CSResourcesFileMapped</key>
	<true/>
</dict>
</plist>
