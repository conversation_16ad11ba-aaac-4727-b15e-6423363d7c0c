-- glslfx version 0.1

//
// Copyright 2020 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdSt/shaders/meshFaceCull.glslfx


--- --------------------------------------------------------------------------
-- glsl MeshFaceCull.Fragment.None

void DiscardBasedOnShading(bool frontFacing, bool isFlipped)
{
    // Nothing to do since h/w face culling is used.
}

--- --------------------------------------------------------------------------
-- glsl MeshFaceCull.Fragment.FrontFacing

void DiscardBasedOnShading(bool frontFacing, bool isFlipped)
{
    if (frontFacing != isFlipped) {
        discard;
    }
}

--- --------------------------------------------------------------------------
-- glsl MeshFaceCull.Fragment.BackFacing

void DiscardBasedOnShading(bool frontFacing, bool isFlipped)
{
    if ((!frontFacing) != isFlipped) {
        discard;
    }
}
