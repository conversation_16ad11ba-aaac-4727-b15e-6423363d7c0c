{"Plugins": [{"Info": {"Types": {"HdSt_DependencySceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Storm Dependency Scene Index"}, "HdSt_DependencyForwardingSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Storm Dependency Forwarding Scene Index"}, "HdSt_MaterialBindingResolvingSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene Index to resolve material bindings."}, "HdSt_MaterialPrimvarTransferSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene Index to transfer primvars from material to prim."}, "HdSt_NodeIdentifierResolvingSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene Index to resolve nodeIdentifier from glslfx sourceAsset."}, "HdSt_ImplicitSurfaceSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene Index to turn implicit surfaces into prims suitable for Storm"}, "HdSt_NurbsApproximatingSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene Index to resolve terminal names."}, "HdSt_TetMeshConversionSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene Index to convert tet meshes into standard triangle based meshes."}, "HdSt_UnboundMaterialPruningSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene index to prune materials that aren't bound."}, "HdSt_VelocityMotionResolvingSceneIndexPlugin": {"bases": ["HdSceneIndexPlugin"], "loadWithRenderer": "GL", "priority": 0, "displayName": "Scene index to resolve velocity-based motion."}}, "ShaderResources": "shaders"}, "LibraryPath": "../../../../../../../../../Binaries/Win64/usd_hdSt.dll", "Name": "hdSt", "ResourcePath": "resources", "Root": "..", "Type": "library"}]}