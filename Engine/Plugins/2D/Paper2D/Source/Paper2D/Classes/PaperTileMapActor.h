// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "GameFramework/Actor.h"

#include "PaperTileMapActor.generated.h"

#define UE_API PAPER2D_API

class UPaperTileMapComponent;

/**
 * An instance of a UPaperTileMap in a level.
 *
 * This actor is created when you drag a tile map asset from the content browser into the level, and
 * it is just a thin wrapper around a UPaperTileMapComponent that actually references the asset.
 */
UCLASS(MinimalAPI, ComponentWrapperClass)
class APaperTileMapActor : public AActor
{
	GENERATED_UCLASS_BODY()

private:
	UPROPERTY(Category=TileMapActor, VisibleAnywhere, BlueprintReadOnly, meta=(ExposeFunctionCategories="Sprite,Rendering,Physics,Components|Sprite", AllowPrivateAccess="true"))
	TObjectPtr<class UPaperTileMapComponent> RenderComponent;
public:

	// AActor interface
#if WITH_EDITOR
	UE_API virtual bool GetReferencedContentObjects(TArray<UObject*>& Objects) const override;
#endif
	// End of AActor interface

	/** Returns RenderComponent subobject **/
	FORCEINLINE class UPaperTileMapComponent* GetRenderComponent() const { return RenderComponent; }
};

#undef UE_API
