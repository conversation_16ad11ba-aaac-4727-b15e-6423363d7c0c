<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>p4api.net v2020.2</Name>
  <Location>//UE5/Main/Engine/Source/ThirdParty/Perforce/P4Api.Net</Location>
  <Function>Adds native P4 api support for the Horde Server</Function>
  <Eula>https://github.com/perforce/p4api.net/blob/master/LICENSE.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>