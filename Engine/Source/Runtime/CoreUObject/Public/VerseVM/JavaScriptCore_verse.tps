<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>JavaScriptCore</Name>
  <Location>//UE5/Main/Engine/Source/Runtime/CoreUObject/Private/VerseVM/VVMHeapInt.cpp</Location>
  <Location>//UE5/Main/Engine/Source/Runtime/CoreUObject/Public/VerseVM/VVMHeapInt.h</Location>
  <Function>Implements a memory heap used by Verse.</Function>
  <Eula>https://github.com/WebKit/webkit/blob/main/Source/JavaScriptCore/runtime/JSBigInt.cpp</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
     <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
