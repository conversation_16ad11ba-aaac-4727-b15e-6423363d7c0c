<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Alias method algorithm from Darts, Dice, and Coins site</Name>
  <Location>/Engine/Source/Runtime/Engine/Private/WeightedRandomSampler.cpp</Location>
  <Date>2017-01-20T14:01:08.4868419+00:00</Date>
  <Function>Algorithm which allows weighted random sampling of elements in constant time</Function>
  <Justification>Initially for sampling meshes for particle systesm. Will likely use it for other things in future.</Justification>
  <Eula>http://www.keithschwarz.com/darts-dice-coins/</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>None</LicenseFolder>
</TpsData>