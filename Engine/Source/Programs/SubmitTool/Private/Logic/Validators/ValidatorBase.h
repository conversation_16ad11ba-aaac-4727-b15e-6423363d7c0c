// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once


#include "CoreMinimal.h"

#include "Logging/SubmitToolLog.h"
#include "Logic/ProcessWrapper.h"
#include "Parameters/SubmitToolParameters.h" 

#include "Logic/Services/SubmitToolServiceProvider.h"
#include "ISourceControlState.h"
#include "ValidatorDefinition.h"
#include "ValidatorOptionsProvider.h"

#include "ValidatorBase.generated.h"

class FTag;
struct FAnalyticsEventAttribute;

namespace SubmitToolParseConstants
{
	const FString TagValidator = TEXT("TagValidator");
	const FString UBTValidator = TEXT("UBTValidator");
	const FString EditorValidator = TEXT("EditorValidator");
	const FString ShaderValidator = TEXT("ShaderValidator");
	const FString CustomValidator = TEXT("CustomValidator");
	const FString CrossChangelistValidator = TEXT("CrossChangelistValidator");
	const FString PreflightValidator = TEXT("PreflightValidator");
	const FString PackageDataValidator = TEXT("PackageDataValidator");
	const FString JsonValidator = TEXT("JsonValidator");
	const FString InvalidateNode = TEXT("Invalidated");
	const FString VirtualizationToolOp = TEXT("VirtualizationTool");
	const FString CoreRedirectsValidator = TEXT("CoreRedirectsValidator");
};

UENUM()
enum class EValidationStates : uint8
{
	Not_Run,
	Skipped,
	Not_Applicable,
	Running,
	Valid,
	Failed,
	Timeout,
	Queued,
	Disabled
};

template<class T>
concept DerivedFromDefinition = std::is_base_of<FValidatorDefinition, T>::value;

class FValidatorBase;
DECLARE_MULTICAST_DELEGATE_OneParam(FOnValidatorFinished, const FValidatorBase& /*Validator*/)

/**
 * @brief Base class for validator.
 */
class FValidatorBase
{
public:
	FValidatorBase() = delete;
	FValidatorBase(const FName& InNameId, const FSubmitToolParameters& InParameters, const TWeakPtr<FSubmitToolServiceProvider> InServiceProvider, const FString& InDefinition);
	virtual ~FValidatorBase();

	virtual void ParseDefinition(const FString& InDefinition);

	virtual const FString& GetValidatorTypeName() const = 0;
	const FName& GetValidatorNameId() const
	{
		return ValidatorNameID;
	}
	const FString& GetValidatorName() const
	{
		return ValidatorName;
	}

	virtual void StartValidation();

	virtual void ToggleEnabled();

	void Invalidate(bool bForce = false)
	{
		if (State == EValidationStates::Disabled)
		{
			return;
		}

		if(State == EValidationStates::Running)
		{
			CancelValidation();
		}
		else if(State != EValidationStates::Queued || bForce)
		{
			State = EValidationStates::Not_Run;
		}
	}

	bool GetHasPassed() const
	{
		return State == EValidationStates::Valid || State == EValidationStates::Skipped || State == EValidationStates::Not_Applicable || State == EValidationStates::Disabled;
	}

	bool GetIsRunningOrQueued() const
	{
		return GetIsRunning() || GetIsQueued();
	}

	bool GetIsRunning() const
	{
		return State == EValidationStates::Running;
	}

	bool GetIsQueued() const
	{
		return State == EValidationStates::Queued;
	}

	float GetRunTime() const
	{
		return RunTime;
	}

	const EValidationStates& GetState()
	{
		return State;
	}

	virtual void Tick(float InDeltaTime);

	virtual void CancelValidation(bool InbAsFailed = false)
	{
		if (State == EValidationStates::Disabled)
		{
			return;
		}

		if(State == EValidationStates::Running)
		{
			StopInternalValidations();
		}

		State = InbAsFailed ? EValidationStates::Failed : EValidationStates::Not_Run;
	}

	virtual void SetQueued(bool InbForceRun = false)
	{
		bForceRun = InbForceRun;
		if(State == EValidationStates::Running)
		{
			StopInternalValidations();
		}

		State = EValidationStates::Queued;
	}

	void SetNotApplicable()
	{
		State = EValidationStates::Not_Applicable;
	}

	virtual bool Activate();

	virtual void InvalidateLocalFileModifications();
	virtual bool EvaluateTagSkip();
	virtual bool IsRelevantToCL() const;
	virtual const FString GetPopupMessageWhenFailedText() const
	{
		return Definition->PopupMessageWhenFailed;
	}

	const FString GetStatusText() const;
	const EValidationStates GetValidatorState() const
	{
		return State;
	}

	const TMap<FString, TMap<FString, FString>>& GetValidatorOptions() const
	{
		return OptionsProvider.GetValidatorOptions();
	}

	const FString GetSelectedOptionValue(const FString& InOptionName) const
	{
		return OptionsProvider.GetSelectedOptionValue(InOptionName);
	}
	const FString GetSelectedOptionKey(const FString& InOptionName) const
	{
		return OptionsProvider.GetSelectedOptionKey(InOptionName);
	}
	EValidatorOptionType GetOptionType(const FString& InOptionName) const
	{
		return OptionsProvider.GetOptionType(InOptionName);
	}

	void SetSelectedOption(const FString& InOptionName, const FString& InOptionValue);

	virtual const TArray<FAnalyticsEventAttribute> GetTelemetryAttributes() const;
	
	bool CanPrintErrors() const;
	void PrintErrorSummary() const;

	const FString GetValidationConfigId() const;

	template<DerivedFromDefinition T>
	const T* GetTypedDefinition() const;

	TArray<FName> Dependants;
	TUniquePtr<const FValidatorDefinition> Definition;
	TMap<FString, TArray<FString>> PathsPerExtension; // Digested from Definition->IncludeFilesInDirectoryPerExtension
	FOnValidatorFinished OnValidationFinished;

protected:
	FName ValidatorNameID;
	FString ValidatorName;
	FValidatorOptionsProvider OptionsProvider;
	TWeakPtr<FSubmitToolServiceProvider> ServiceProvider;
	TArray<FSourceControlStateRef> FilteredFiles;
	TArray<FString> ActivationErrors;
	mutable TArray<FString> ErrorListCache;
	bool bIsValidSetup = false;
	bool bForceRun = false;
	const FSubmitToolParameters& SubmitToolParameters;

	FDateTime Start;
	float RunTime = 0;

	/**
	 * @brief Starts the validator.
	 * @param SubmitSettings The submit settings to use for this validation cycle.
	 * @return Returns true if validation has started successfully, false if validation failed.
	 */
	virtual bool Validate(const FString& CLDescription, const TArray<FSourceControlStateRef>& FilteredFilesInCL, const TArray<const FTag*>& Tags) = 0;

	virtual bool AppliesToFile(const FSourceControlStateRef InFile, bool InbAllowIncremental, bool& OutbIsIncrementalSkip) const;
	virtual bool AppliesToCL(const FString& CLDescription, const TArray<FSourceControlStateRef>& FilesInCL, const TArray<const FTag*>& Tags, TArray<FSourceControlStateRef>& OutFilteredFiles, TArray<FSourceControlStateRef>& OutIncrementalSkips, bool InbAllowIncremental) const;
	virtual void StopInternalValidations()
	{};
	virtual void ValidationFinished(const bool bHasPassed);

	virtual void LogFailure(const FString& FormattedMsg) const
	{
		if (Definition->IsRequired)
		{
			UE_LOG(LogValidators, Error, TEXT("%s"), *FormattedMsg);
			UE_LOG(LogValidatorsResult, Error, TEXT("%s"), *FormattedMsg);
		}
		else
		{
			UE_LOG(LogValidators, Warning, TEXT("%s"), *FormattedMsg);
			UE_LOG(LogValidatorsResult, Warning, TEXT("%s"), *FormattedMsg);
		}

		FScopeLock Lock(&Mutex);
		ErrorListCache.Add(FormattedMsg);
	}

	virtual void Skip()
	{
		State = EValidationStates::Skipped; OnValidationFinished.Broadcast(*this);
	}
private:
	EValidationStates State = EValidationStates::Not_Run;


	mutable TMap<uint32, bool> FileHashes;
	mutable FCriticalSection Mutex;
};

template<DerivedFromDefinition T>
inline const T* FValidatorBase::GetTypedDefinition() const
{
	return  static_cast<const T*>(Definition.Get());
}
