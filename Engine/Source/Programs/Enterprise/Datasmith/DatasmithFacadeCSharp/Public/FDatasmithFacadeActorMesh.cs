// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeActorMesh : FDatasmithFacadeActor {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;

  internal FDatasmithFacadeActorMesh(global::System.IntPtr cPtr, bool cMemoryOwn) : base(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeActorMesh obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  protected override void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeActorMesh(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
      base.Dispose(disposing);
    }
  }

  public FDatasmithFacadeActorMesh(string InElementName) : this(DatasmithFacadeCSharpPINVOKE.new_FDatasmithFacadeActorMesh(InElementName), true) {
  }

  public void SetMesh(string InMeshName) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_SetMesh(swigCPtr, InMeshName);
  }

  public string GetMeshName() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_GetMeshName(swigCPtr));
    return ret;
  }

  public void AddMaterialOverride(string MaterialName, int Id) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_AddMaterialOverride__SWIG_0(swigCPtr, MaterialName, Id);
  }

  public void AddMaterialOverride(FDatasmithFacadeMaterialID Material) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_AddMaterialOverride__SWIG_1(swigCPtr, FDatasmithFacadeMaterialID.getCPtr(Material));
    if (DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Pending) throw DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Retrieve();
  }

  public int GetMaterialOverridesCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_GetMaterialOverridesCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeMaterialID GetMaterialOverride(int MaterialOverrideIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_GetMaterialOverride(swigCPtr, MaterialOverrideIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeMaterialID(objectPtr, true);
	}
}

  public void RemoveMaterialOverride(FDatasmithFacadeMaterialID Material) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_RemoveMaterialOverride(swigCPtr, FDatasmithFacadeMaterialID.getCPtr(Material));
    if (DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Pending) throw DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Retrieve();
  }

  public void ResetMaterialOverrides() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorMesh_ResetMaterialOverrides(swigCPtr);
  }

}
